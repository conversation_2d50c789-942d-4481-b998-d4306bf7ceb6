/**
 *  @module DatadeviceController
 *  @description  Datadevice Controller module handles tasks relating to incoming and outgoing data for devices
 */
const moment = require("moment-timezone");
const unitService = require("../services/unitService");
const parametersService = require("../services/parametersService");
const dataDeviceService = require("../services/dataDeviceService");
const helper = require("../services/helper");
const influx = require('../services/influxEnterPriseService');
const cacheService = require("../services/cacheService");
moment.tz.add(
	"Asia/Kolkata|HMT +0630 IST|-5R.k -6u -5u|01212|-18LFR.k 1unn.k HB0 7zX0|15e6"
);

const RAMA_PRESET = ["runhour", "tonsteamperproduct", "tonelecperproduct"];
moment.tz.setDefault("Asia/Kolkata");
module.exports = {
	"dashBoardV1": async (req, res) => {
		let { siteId } = req.allParams();

		{
			/**
			 * MOCKING DASHBOARD API FOR IBMS PRESENTATION ONLY. We are using mgch site for demo purposes
			 * */
			const sunshineIBMSSites = ['be-hyd', 'suh-hyd'];
			if (sunshineIBMSSites.indexOf(siteId) !== -1) {
				siteId = "mgch"
			}
		}
		

		let _userPrefernce = req._userMeta.unitPref;
		let consParam = await dailyConsumptionService.getUserConsumptionPreference(_userPrefernce, siteId);
		let currentLoad = null,
			emData;
		if (!siteId) return res.badRequest();
		let key = `${siteId}_mainMeter`;
		try {
			let data = await DyanmoKeyStore.findOne({ key });
			if (!data) {
				return res.badRequest();
			}
			let emList = data.value.split(",");
			emData = await getLastDayData(emList);
			const weekData = dailyConsumptionService.getWeekConsumption_new(siteId, consParam);
			let lastTs = await cacheService.get(`${siteId}_lastDataTime`);
			let lastTimeStamp = parseInt(lastTs) || null;
			let tsMap = tsDataMap(emData);
			let hrMap = groupByHour(tsMap);
			let response = getDashboardParams(hrMap, lastTimeStamp,siteId);
			let weekConsData = await weekData;
			let weekConsPattern = weekConsData.weekConsPattern;
			let weekCons = weekConsData.weekCons;
			let load = currentLoad || "NA";
			return res.ok({
				...response,
				load,
				lastTimeStamp,
				weekConsPattern,
				weekCons,
			});
		} catch (e) {
			sails.log.error(e);
			return res.serverError();
		}
		/**
	 *
	 * {
		lastTimeStamp: null,
		loadPattern: [],
		load: "NA",
		hCons: "NA",
		dCons: "NA",
		weekCons: "NA",
		weekConsPattern: []
	  }
	 */
		/** HELPERS */
		function groupByHour(tsDataMap) {
			let finalLoad = {},
				finalCons = {};
			for (let ts in tsDataMap) {
				let { load, cons } = tsDataMap[ts];
				if (!currentLoad) currentLoad = load[0];
				// Commenting this check to ensure Load Pattern forms in LMW. Not sure of it's purpose.
				// load[1] seems to contain the number of energy meters that contributed to that load and is assigned in the tsDataMap function
				// I don't understand why should the load pattern for that minute should be skipped if a single energy meter had null values.
				// It doesn't interfere with the calculation.
				// if (load[1] === emData.length) {
					let startHr =
						moment(ts)
							.startOf("h")
							.unix() * 1000;
					if (!finalLoad[startHr]) {
						finalLoad[startHr] = {
							"load": load[0],
							"count": 1,
						};
					} else {
						finalLoad[startHr]["load"] += load[0];
						finalLoad[startHr]["count"] += 1;
					}
					finalCons[ts] = cons[0];
				// }
			}

			return { finalLoad, finalCons };
		}
		function getLastDayData(deviceIDs) {
			let lastDay = moment()
				.subtract(1, "d")
				.format("YYYY-MM-DD HH:mm");
			let dataPromise = deviceIDs.map(deviceId =>
				Datadevice.find({
					deviceId,
					"timestamp": { "gte": lastDay },
					"sort": "-1",
				})
			);
			return Promise.all(dataPromise);
		}
		function tsDataMap(emData) {
			/** emData = [[dataOfEM1],[dataOfEM2]] */
			let tsDataMap = {};

			emData.map(dataArr => {
				dataArr.map(devData => {
					let { timestamp, data } = devData;
					let consData = helper.getConsumptionFromDeviceData(data, consParam);

					if (!tsDataMap[timestamp]) {
						if (data.kw != "null" && consData != "null")
							tsDataMap[timestamp] = {
								"load": [parseInt(data.kw), 1],
								"cons": [parseInt(consData), 1],
							};
					} else {
						if (data.kw != "null" && consData != "null") {
							tsDataMap[timestamp].load[0] += parseInt(data.kw);
							tsDataMap[timestamp].load[1] += 1;
							tsDataMap[timestamp].cons[0] += parseInt(
								consData
							);
							tsDataMap[timestamp].cons[1] += 1;
						}
					}
				});
			});

			return tsDataMap;
		}
		function getDashboardParams(data,ts,siteId) {
			let loadObj = data.finalLoad;
			let loadPattern = [];
			let { finalCons } = data;
			let consArr = Object.keys(finalCons);
			let dayStart = moment()
			.startOf("day")
			.format("YYYY-MM-DD HH:mm:ss");
			if(siteId === 'lmw-coi'){
				dayStart = moment()
				.startOf("day")
				.format("YYYY-MM-DD 01:00:00");
			}

			let firstDataPointTS =
				consArr.filter(date => date <= dayStart)[0] ||
				consArr[consArr.length - 1];
			let hCons = finalCons[consArr[0]] - finalCons[consArr[58]];
			let dCons = finalCons[consArr[0]] - finalCons[firstDataPointTS];
			for (let ts in loadObj) {
				let unixTs = ts;
				let load = loadObj[ts]["load"] / loadObj[ts]["count"];
				if (typeof ts === "string") unixTs = parseInt(ts);
				loadPattern.push([unixTs, Math.round(load)]);
			}
			loadPattern.reverse();
			return { loadPattern, hCons, dCons };
			/*return res.ok({
		lastTimeStamp: null,
		loadPattern: [],
		load: "NA",
		hCons: "NA",
		dCons: "NA",
		weekCons: "NA",
		weekConsPattern: []
	  })*/
		}
	},
	"getLastDayLoad": function (req, res) {
		let siteId = req.param("siteId");
		if (!siteId) return res.badRequest({ "err": "Invalid Parameters" });
		cacheService
			.getLastDayLoad(siteId)
			.then(data => {
				res.ok({ "data": data.resArr, "curentLoad": data.currentLoad });
			})
			.catch(err => {
				sails.log.error(err);
				res.ok({ "data": null, "curentLoad": null });
			});
	},
	"getMonthlyConsumption": (req, res) => {
		let siteId = req.param("siteId");

		baselineService
			.getCurrentBaseline(siteId, true)
			.then(baseline => {
				if (Array.isArray(baseline)) {
					baseline = baseline[0];
				}
				let ts = baseline.startDate;
				let searchObj = {
					"siteId": siteId,
					"timestamp": {
						"gte": ts,
					},
				};
				DailyConsumption.find(searchObj)
					.then(function (data) {
						let cons = 0;
						if (data.length == 0) {
							return res.ok({ "consumption": cons });
						}

						for (let i = 0; i < data.length; i++) {
							cons += parseInt(data[i].actual);
						}
						return res.ok({ "consumption": cons });
					})
					.catch(err => {
						sails.log.error(err);
						return res.serverError({
							"err": "Internal Server Error",
						});
					});
			})
			.catch(err => {
				sails.log.error(err);
				return res.ok({ "data": null, "err": err.err });
			});
	},
	"getMonthlyConsumption_new": async (req, res) => {
		let siteId = req.param("siteId"), value;
		let _userPrefernce = req._userMeta.unitPref;
		{
			/**
			 * MOCKING DASHBOARD API FOR IBMS PRESENTATION ONLY. We are using mgch site for demo purposes
			 * */
			const sunshineIBMSSites = ['be-hyd', 'suh-hyd'];
			if (sunshineIBMSSites.indexOf(siteId) !== -1) {
			  siteId = "mgch"
			}
		  }
		let siteConsumptionUnit = await dailyConsumptionService.getUserConsumptionPreference(_userPrefernce, siteId);

		baselineService
			.getCurrentBaseline(siteId, true)
			.then(baseline => {
				if (Array.isArray(baseline)) {
					baseline = baseline[0];
				}
				let ts = baseline.startDate;
				let searchObj = {
					"siteId": siteId,
					"timestamp": {
						"gte": ts,
					},
				};
				DailyConsumption.find(searchObj)
					.then(function (data) {
						let cons = 0;
						if (data.length == 0) {
							return res.ok({ "consumption": cons });
						}

						for (let i = 0; i < data.length; i++) {

							value = dailyConsumptionService.getConsumptionAccordingToUnit(data[i], siteConsumptionUnit);

							cons += value;
						}
						return res.ok({ "consumption": cons });
					})
					.catch(err => {
						sails.log.error(err);
						return res.serverError({
							"err": "Internal Server Error",
						});
					});
			})
			.catch(err => {
				sails.log.error(err);
				return res.ok({ "data": null, "err": err.err });
			});
	},

	"getDevices": (req, res) => {
		let type = req.params.type;
		let siteId = req.params.siteId;
		sails.log.info("req came", type);
		sails.log.info("req came", siteId);
		Devices.find({ "siteId": siteId }).then(devices => {
			let realSend = [];
			devices.forEach(d => {
				if (Boolean(d.deviceType) && d.deviceType == type) {
					realSend.push(d);
				}
			});
			res.ok(realSend);
		});
	},
	"getWeeklyConsumption": function (req, res) {
		let searchObj = {
			"where": {
				"siteId": req.param("siteId"),
				"timestamp": {
					"gte": moment()
						.subtract(7, "days")
						.format("YYYY-MM-DD"),
				},
			},
			"limit": 7,
			"sort": "-1",
		};
		DailyConsumption.find(searchObj).exec(function (err, data) {
			if (err) {
				sails.log.error(err);
				return res.ok({
					"data": null,
					"err": "Unable to fetch records at present!",
				});
			}
			if (!data || data.length == 0) {
				return res.ok({
					"data": null,
					"err": "Data not available for current site",
				});
			}
			let totalWeeklyConsumption = 0;
			for (let i = 0; i < data.length; i++) {
				totalWeeklyConsumption += data[i].actual;
			}
			return res.json(200, {
				"totalWeeklyConsumption": totalWeeklyConsumption,
				"7DaysConsumption": data,
			});
		});
	},
	"getWeeklyConsumption_new": async function (req, res) {

		let siteId = req.param("siteId"), value, err;
		let _userPrefernce = req._userMeta.unitPref;

		let siteConsumptionUnit = await dailyConsumptionService.getUserConsumptionPreference(_userPrefernce, siteId);

		let searchObj = {
			"where": {
				"siteId": siteId,
				"timestamp": {
					"gte": moment()
						.subtract(7, "days")
						.format("YYYY-MM-DD"),
				},
			},
			"limit": 7,
			"sort": "-1",
		};

		DailyConsumption.find(searchObj).then((data) => {
			if (err) {
				sails.log.error(err);
				return res.ok({
					"data": null,
					"err": "Unable to fetch records at present!",
				});
			}
			if (!data || data.length == 0) {
				return res.ok({
					"data": null,
					"err": "Data not available for current site",
				});
			}
			let totalWeeklyConsumption = 0;
			for (let i = 0; i < data.length; i++) {
				value = dailyConsumptionService.getConsumptionAccordingToUnit(data[i], siteConsumptionUnit);
				data[i]["actual"] = value;
				totalWeeklyConsumption += value;
			}
			return res.json(200, {
				"totalWeeklyConsumption": totalWeeklyConsumption,
				"7DaysConsumption": data,
			});
		}).catch(e => {
			sails.log.error(e);
			return res.json(500, {
				"data": null,
				"err": "Internal server error!",
			});
		});
	},

	//To get all the data between two dates without any limit
	"getData": function (req, res) {
		let start, end, deviceId;
		deviceId = req.param("deviceId");
		if (req.query.start && moment(req.query.start).isValid())
			start = moment(req.query.start).format("YYYY-MM-DD HH:mm:ss");
		else start = undefined;

		if (req.query.end && moment(req.query.start).isValid())
			end = moment(req.query.end).format("YYYY-MM-DD HH:mm:ss");
		else end = undefined;
		if (!start || !end || !deviceId) {
			return res.badRequest({
				"err": "Parameters not valid",
				"data": null,
			});
		}

		let searchObj = {
			"where": {
				"deviceId": deviceId,
				"timestamp": {
					"between": [start, end],
				},
				"sort": "-1",
			},
		};

		Datadevice.find(searchObj).exec((err, data) => {
			if (err) {
				sails.log.error(err, searchObj);
				return res.serverError({
					"err": "Problem reaching datastore ",
					"data": null,
				});
			}
			return res.ok({ "err": null, "data": data });
		});
	},

	"getCusmptionFromCurrentBaseline": function (req, res) {
		let siteId = req.param("siteId");
		if (!siteId) {
			return res.badRequest({
				"err": "Please consult with API docs",
				"data": null,
			});
		}
		baselineService
			.getCurrentBaseline(siteId, true)
			.then(data => {
				let searchObj = {
					"siteId": siteId,
					"timestamp": {
						"gte": moment(data.startDate).format("YYYY-MM-DD"),
					},
				};

				DailyConsumption.find(searchObj)
					.then(consumption => {
						if (!consumption || consumption.length == 0)
							return res.ok({
								"data": null,
								"err": "Data not present for current site",
							});
						return res.ok({ "err": null, "data": consumption });
					})
					.catch(err => {
						sails.log.error(err);
						return res.ok({
							"err": "Unable to fetch data at present",
							"data": null,
						});
					});
			})
			.catch(err => {
				return res.ok({
					"err": "Baeline Not Configured for current site.",
					"data": null,
				});
			});
	},

	//this endpoint gives the latest devicedata for the map part

	"getADeviceLatestData": function (req, res) {
		let deviceId = req.param("deviceId");
		let searchObj = {
			"where": {
				"deviceId": deviceId,
				"limit": 1,
				"sort": "-1",
			},
		};
		Datadevice.find(searchObj).exec(function (err, data) {
			if (err) sails.log.error(err);
			return res.ok(data);
		});
	},
	"consumptionPageData": async (req, res) => {
		try {
			let siteId = req._userMeta._site;
			let userId = req._userMeta.id;
			let emList, $userPreferenceUpdate;

			let userPreference = await UserSiteMap.findOne({ userId, siteId });
			// If configuration not present in user preferences, fetching site configuration and storing in user preferences.
			if (!userPreference.consumptionPageEMList) {
				emList = await DyanmoKeyStore.findOne({ "key": `${siteId}_em` });
				emList = emList.list;
				$userPreferenceUpdate = UserSiteMap.update({ userId, siteId }, { "consumptionPageEMList": emList });
			} else {
				emList = userPreference.consumptionPageEMList;
			}

			if (!emList) {
				return res.ok({
					"err": "Energy Meter Data Not Configured in Keystore",
					"data": null
				});
			} else {
				cacheService.getConsumptionDashboard(req, res, emList);
			}

			try {
				await $userPreferenceUpdate;
			} catch (error) {
				sails.log.error(`[consumptionPageEMList] Error updating Consumption Page EM list in user preference for user: ${userId} in siteId: ${siteId}`);
			}
		} catch (error) {
			sails.log.error("[consumptionPageData] Server Error!", error);
			return res.serverError();
		}
	},
	"dashboardConsumptionData": function (req, res) {
		DyanmoKeyStore.findOne({ "key": `${req.param("siteId")}_em` })
			.then(data => {
				if (!data || !data.list) {
					return res.ok({
						"err": "Energy Meter Data Not Configured in Keystore",
						"data": null,
					});
				} else {
					cacheService.getConsumptionDashboard(req, res, data.list);
				}
			})
			.catch(err => {
				sails.log.error(err);
				return res.ok({
					"err": "Energy Meter Data Not Configured in Keystore",
					"data": null,
				});
				//cacheService.getConsumptionDashboard(req, res, null);
			});
	},
	"dashboardConsumptionData_new": async function (req, res) {

		try {
			let siteId = req.param("siteId");

			{
				/**
				 * MOCKING DASHBOARD API FOR IBMS PRESENTATION ONLY. We are using mgch site for demo purposes
				 * */
				const sunshineIBMSSites = ['be-hyd', 'suh-hyd'];
				if (sunshineIBMSSites.indexOf(siteId) !== -1) {
					siteId = "mgch"
				}
			}
			let _userPrefernce = req._userMeta.unitPref;
			let siteConsumptionUnit = await dailyConsumptionService.getUserConsumptionPreference(_userPrefernce, siteId);

			DyanmoKeyStore.findOne({ "key": `${siteId}_em` })
				.then(data => {
					if (!data || !data.list) {
						return res.ok({
							"err": "Energy Meter Data Not Configured in Keystore",
							"data": null,
						});
					} else {
						cacheService.getConsumptionDashboard_new(req, res, data.list, siteConsumptionUnit);
					}
				})
				.catch(err => {
					sails.log.error(err);
					return res.ok({
						"err": "Energy Meter Data Not Configured in Keystore",
						"data": null,
					});
					//cacheService.getConsumptionDashboard(req, res, null);
				});

		} catch (e) {
			sails.log.error(e);
			return res.serverError();
		}
	},
	"getComponentDataByParam": function (req, res) {
		let componentId = req.param("id");
		let params = req.param("params");
		if (!componentId || !params)
			return res.badRequest({ "err": "Insufficient Parameters" });
		let from = req.param("from");
		if (from && moment(from).isValid)
			from = moment(from).format("YYYY-MM-DD HH:mm");
		let to = req.param("to");
		if (to && moment(to).isValid)
			to = moment(to).format("YYYY-MM-DD HH:mm");
		let groupBy = req.param("groupBy");
		let searchObj = dataDeviceService.generateSearchObj(
			componentId,
			from,
			to
		);
		if (!searchObj) return res.badRequest({ "err": "Invalid Parameters" });

		Datadevice.find(searchObj).exec(function (err, data) {
			if (err) {
				sails.log.error(err);
				return res.badRequest({
					"err": "Unable to fetch data at present",
				});
			}

			if (!params && !groupBy) res.ok(data);
			else {
				dataDeviceService.filterData(data, groupBy, params, res);
			}
		});
	},
	"getComponentData": async (req, res) => {
		let compId = req.param("id");
		let from = req.query.from;
		let to = req.query.to;

		if (from && moment(from).isValid())
			from = moment(from).format("YYYY-MM-DD HH:mm");
		else from = undefined;

		if (to && moment(to).isValid())
			to = moment(to).format("YYYY-MM-DD HH:mm");
		let searchObj;
		if (!compId) {
			return res.badRequest({ "err": "Invalid request parameters" });
		}
		if (!from && !to) {
			searchObj = {
				"where": {
					"deviceId": compId,
				},
				"limit": 1,
				"sort": "-1",
			};
		} else if (from && !to) {
			searchObj = {
				"where": {
					"deviceId": compId,
					"timestamp": {
						"gte": from,
					},
				},
				"sort": "-1",
			};
		} else if (from && to) {
			if (to < from) {
				return res.badRequest({ "err": "From is greater than To" });
			}
			searchObj = {
				"where": {
					"deviceId": compId,
					"timestamp": {
						"between": [from, to],
					},
				},
				"sort": "-1",
			};
		} else {
			return res.badRequest({ "err": "Insufficient Parameters" });
		}
		try {
			let resData = await Datadevice.find(searchObj);
			return res.ok(resData);
		} catch (e) {
			sails.log.error(e);
			return res.serverError({ "err": "Unable to fetch data" });
		}
	},

	// curl -X POST --data "startTime=2018-09-04&endTime=2018-09-5&deviceId=147&params=KVA&group=minutes" http://localhost:1337/analytics/line [line:candel:spec:box]
	/**
	 * @function deviceInfo
	 * @description used for extracting data from differnt devices and filtering them on the basis of type
	 * parameter as per highcharts requirements. This API is used in getting data for different graph types
	 * in JouleTrack
	 * @param {Object} req
	 * The parameters are suported are
	 * startTime - Time in any valid ISO format
	 * endTime - Time in any valid ISO format
	 * deviceId - Array of deviceId for which data is to be extracted,
	 * parameters - Array of parameters ,
	 * groubBy - Aggregation of parameters will be done based on this parameter,
	 * type - graph type for which data is requested
	 * @param {Object} res
	 * @returns {Object} Data with keys as devices id and each parameter filtered based
	 * on the type parameter
	 */
	"deviceInfo": function (req, res) {
		let startTime, endTime, deviceId, parameters, groupBy, type;
		try {
			startTime = moment(req.body.startTime).format("YYYY-MM-DD HH:mm");
			endTime =
				req.body.endTime != "" ? moment(req.body.endTime) : moment();
			groupBy = req.body.group ? req.body.group : "m";
			let temptime = endTime.isValid();
			if (!temptime) {
				return res.send("Invalid end date");
			} else {
				endTime = moment(req.body.endTime)
					.add(1, groupBy)
					.format("YYYY-MM-DD HH:mm");
			}
			if (startTime != "") {
				let temp = moment(endTime) - moment(startTime);
				if (temp < 0) {
					res.send("Paramaters are not complete");
					return;
				}
			} else {
				res.send("Paramaters are not complete");
				return;
			}
			deviceId = req.body.deviceId.split(",");
			if (deviceId.constructor.name != "Array") deviceId = [deviceId];
			parameters = req.body.params.toLowerCase().split(",");
			if (parameters.constructor.name != "Array")
				parameters = [parameters];
			type = req.params.type;
			let promise1 = this.getPlotDateFunction(
				startTime,
				endTime,
				deviceId,
				parameters,
				type,
				groupBy
			);
			let promise2 = this.getMetaData(
				startTime,
				endTime,
				deviceId,
				parameters,
				type
			);

			Promise.all([promise1, promise2])
				.then(data => {
					let resObj = {};
					resObj["plot"] = data[0];
					resObj = { ...resObj, ...data[1] };
					let realSendObj = {};
					let { plot, modes } = resObj;
					deviceId.forEach(id => {
						realSendObj[id] = {
							"plot": {},
							"user": {},
							"maintainance": {},
							"modes": {},
						};
						for (let j in parameters) {
							let parameter = parameters[j];
							if (type === "line") {
								let transformedData = helper.addNullGroupBy(
									plot[id][parameter],
									groupBy
								);
								plot[id][parameter] = transformedData || [];
							}
						}
						realSendObj[id]["plot"] = plot[id];
						realSendObj[id]["modes"] = modes[id];
					});
					for (let j in parameters) {
						for (let i in realSendObj) {
							let parameter = parameters[j];
							if (!realSendObj[i]["plot"][parameter]) {
								realSendObj[i]["plot"][parameter] = [];
							}
						}
					}
					return realSendObj;
				})
				// Unit Conversion DAU start Begins
				.then(function unitConversion(responseObject) {
					let $paramGroup = [];
					let data = responseObject;
					let { _site } = req._userMeta;
					//getting paramGroup for each parameter, to be removed when paramgroup
					//comes from frontend
					Object.keys(data).forEach(deviceId => {
						let isComponent = false;
						if (deviceId.split("_").length === 2)
							isComponent = true;
						let paramData = data[deviceId]["plot"];
						Object.keys(paramData).forEach(paramKey => {
							let $param = null;
							if (isComponent) {
								$param = componentService
									.queryDataKeys(
										deviceId,
										{ "key": paramKey },
										["dau", "paramGroup"]
									)
									.then(paramInfo => {
										if (
											!paramInfo ||
											paramInfo.length === 0
										) {
											paramInfo = [
												{
													"dau": "unitless",
													"paramGroup": "NA",
												},
											];
										}
										return {
											deviceId,
											"param": paramKey,
											"dau": paramInfo[0].dau,
											"paramGroup":
												paramInfo[0].paramGroup,
										};
									});
							} else {
								$param = parametersService
									.getParamGroup(_site, deviceId, paramKey)
									.then(paramInfo => {
										// case if a parameter value does not exist
										// show data but log an error

										if (!paramInfo) {
											//sails.log.error(
											//	"Error, Parameter Not exist",
											//	deviceId,
											//	paramKey
											//);
											paramInfo = {
												"dau": "unitless",
												"paramGroup": "NA",
											};
										}
										return {
											deviceId,
											"param": paramKey,
											"dau": paramInfo.dau,
											"paramGroup": paramInfo.paramGroup,
										};
									})
									.catch(err => {
										sails.log.error("[DatadeviceController > deviceInfo] Error", err);
										return;
									});
							}
							$paramGroup.push($param);
						});
					});
					Promise.all($paramGroup)
						.then(dauList => {
							let devParmMap = dauList
								.filter(Boolean)
								.reduce((map, obj) => {
									map[`${obj.deviceId}-${obj.param}`] = obj;
									return map;
								}, {});
							return devParmMap;
						})
						// this codeblock can be removed when paramgroup is sent
						// from frontend
						.then(devDauMap => {
							Object.keys(data).forEach(deviceId => {
								let { unitPref } = req._userMeta;
								let paramData = data[deviceId]["plot"];
								Object.keys(paramData).forEach(paramKey => {
									let searchKey = `${deviceId}-${paramKey}`;
									if (!devDauMap[searchKey]) return;
									let { dau, paramGroup } = devDauMap[
										searchKey
									];
									let deviceData = paramData[paramKey];
									if (unitPref[paramGroup] !== dau) {
										for (
											let i = 0;
											i < deviceData.length;
											i++
										) {
											let val = deviceData[i][1];
											val = unitService.filter(
												paramGroup,
												unitPref[paramGroup],
												val
											);
											deviceData[i][1] = val;
										}
									}
								});
							});
							return res.ok(responseObject);
						})
						.catch(err => {
							sails.log.error(err);
							return res.ok(responseObject);
						});
				});
		} catch (e) {
			sails.log.error(e);
			res.send("Parameters are not complete");
			return;
		}
	},

	"getMetaData": async function (
		startTime,
		endTime,
		deviceId,
		parameters,
		type
	) {
		let mainObj = { "user": [], "maintainance": [], "modes": {} };
		let tempObj = {};
		let ctrlIds = [];
		let cmdMap = {
			"Actuator": "actuator",
			"Status": "start",
			"EM": "frequency",
			"thermostat": "thermostat",
		};
		let utilMap = {};
		if (type !== "line") {
			return mainObj;
		}
		return new Promise(async function (res, rej) {
			try {
				for (let i = 0; i < deviceId.length; i++) {
					let ComponentTypeId = false;
					let did = deviceId[i];
					try {
						did = parseInt(did);
						if (isNaN(did)) {
							did = deviceId[i];
							ComponentTypeId = true;
						}
					} catch (e) {
						sails.log.error(e);
					}

					if (ComponentTypeId) {
						// id is like ssh_x
						await Component.findOne({
							"deviceId": did,
						}).then(async component => {
							// let controls = typeof (component.controls) != "object" ? JSON.parse(component.controls) : component.controls;
							let dataIds =
								typeof component.data != "object"
									? JSON.parse(component.data)
									: component.data;

							for (
								let param = 0;
								param < parameters.length;
								param++
							) {
								let parameter = parameters[param];
								if (cmdMap[parameter]) {
									// here we can add also parameter
									let totalUseId =
										dataIds[parameter] + "_" + parameter;
									ctrlIds.push(totalUseId);
									utilMap[totalUseId] = parameter;
								}
								try {
									let resModes = await dataDeviceService.getMode(
										ctrlIds,
										startTime,
										endTime
									);
									for (let cdid in resModes) {
										tempObj[utilMap[cdid]] = resModes[cdid];
										mainObj["modes"][did] = tempObj;
									}
								} catch (e) {
									sails.log.error(e);
								}
							}
						});
					} else {
						// id is like 1234, and parameters like cwit,cwot
						let idsList = [];
						for (
							let param = 0;
							param < parameters.length;
							param++
						) {
							idsList.push(did + "_" + parameters[param]);
						}
						let secresData = await dataDeviceService.getMode(
							idsList,
							startTime,
							endTime
						);
						let resData = {};
						for (let p in secresData) {
							let paramAsked = p.split("_")[1];
							resData[paramAsked] = secresData[p];
						}
						mainObj["modes"][did] = resData;
					}
				}

				res(mainObj);
			} catch (e) {
				sails.log.error(e);
				res(mainObj);
			}
		});
	},
	"getPlotDateFunction": function (
		startTime,
		endTime,
		deviceId,
		parameters,
		type,
		groupBy
	) {
		return new Promise((res, rej) => {
			let retu_result = {};
			let total = deviceId.length;

			let obj = {
				"startTime": startTime,
				"endTime": endTime,
				"reqst": parameters,
				"groupBy": groupBy,
				"type": type,
			};

			async function dataCompleter() {
				const data= await sails.config.helper.getDataFromInflux(obj);
				return data;
			}

			async function dataWrapper() {
				// iterate on all devices ssh_1,ssh_2 .. and add this device onto 'obj', and call getDate(obj)
				while (total > 0) {
					total -= 1;
					obj["deviceId"] = deviceId[total];
					try {
						let r = await dataCompleter();
						retu_result[deviceId[total]] = r[0];
					} catch (e) {
						retu_result = { "Error": e };
					}
				}
				res(retu_result);
			}
			dataWrapper();
		});
	},
	"analyzeCustomAxis": (req, res) => {
		console.error(`[DynamoDB DataDevices] function=analyzeCustomAxis method=${req.method} route=${req.route ? req.route.path : req.url}, statusCode=${res.statusCode}`);
		/*
	  xAxis:{
		device:
		param:
	  }
	  yAxis:{
		device:
		param:
	  }
	*/
		//update DAU wala code.
		let startTime, endTime, xAxis, yAxis, xParamGroup, yParamGroup;
		let { unitPref } = req._userMeta;
		try {
			startTime = moment(req.body.startTime).format("YYYY-MM-DD HH:mm");
			endTime =
				req.body.endTime != "" ? moment(req.body.endTime) : moment();
			xAxis = req.body.xAxis;
			yAxis = req.body.yAxis;
			xParamGroup = xAxis.paramGroup;
			yParamGroup = yAxis.paramGroup;
			let temptime = endTime.isValid();
			if (!temptime) {
				return res.badRequest("Invalid end date");
			} else {
				endTime = moment(req.body.endTime).format("YYYY-MM-DD HH:mm");
			}
			if (startTime != "") {
				let temp = moment(endTime) - moment(startTime);
				if (temp < 0) {
					res.badRequest("Parameters are not complete");
					return;
				}
			} else {
				res.badRequest("Parameters are not complete");
				return;
			}
			if (!xAxis || !xAxis.deviceId || !xAxis.param) {
				return res.badRequest("Please specify axis");
			}
			if (!yAxis || !yAxis.deviceId || !yAxis.param) {
				return res.badRequest("Please specify axis");
			}

			//All set ready to query

			let searchObjX = {
				"startTime": startTime,
				"endTime": endTime,
				"deviceId": xAxis.deviceId,
				"type": "custom",
				"groupBy": "minutes",
			};
			let searchObjY = {
				"startTime": startTime,
				"endTime": endTime,
				"deviceId": yAxis.deviceId,
				"type": "custom",
				"groupBy": "minutes",
			};
			let xPromise = sails.config.helper.getData(searchObjX);
			let yPromise = sails.config.helper.getData(searchObjY);
			let finalObj = {};
			let countObj = {};
			let resObj = [];
			Promise.all([xPromise, yPromise])
				.then(d => {
					let xCords = dataDeviceService.filterX(d[0][0], xAxis);
					let yCords = dataDeviceService.filterY(d[1][0], yAxis);
					xCords.forEach((ele, index) => {
						let xPt = Number(ele[1]);
						let ts = Number(ele[0]);
						let yPt = Number(yCords[ts]);

						if (!finalObj[xPt]) {
							finalObj[xPt] = yPt;
							countObj[xPt] = 1;
						} else {
							finalObj[xPt] += yPt;
							countObj[xPt] += 1;
						}
					});
					for (const xPt in countObj) {
						if (countObj.hasOwnProperty(xPt)) {
							const count = countObj[xPt];
							let yPt = helper.returnFilteredNumber(
								finalObj[xPt] / count
							);
							resObj.push([xPt, yPt]);
						}
					}
					resObj.sort((a, b) => a[0] - b[0]);
					if (xParamGroup &&
						yParamGroup
					) {
						let xUserPref = unitPref[xParamGroup];
						let yUserPref = unitPref[yParamGroup];
						resObj = unitService.filterBoth(
							xParamGroup,
							yParamGroup,
							xUserPref,
							yUserPref,
							resObj
						);
					}
					return res.ok({ "data": resObj });
					// Unit Service Logic Begins Here
					// let { _site, unitPref } = req._userMeta;
					// // unitPref = { "temperature": "degF", "pressure": "bar" }; // This Is For Testing{To Be Remove When tested}
					// let paramGroup = Object.keys(unitPref);
					// const promises = [];
					// const xPromise = parametersService.getParamGroup(
					// 	_site,
					// 	xAxis.deviceId,
					// 	xAxis.param
					// );
					// const yPromise = parametersService.getParamGroup(
					// 	_site,
					// 	yAxis.deviceId,
					// 	yAxis.param
					// );
					// promises.push(xPromise);
					// promises.push(yPromise);
					// Promise.all(promises)
					// 	.then(response => {
					// 		const xData = response[0];
					// 		const yData = response[1];
					// 		const xParamGroup = xData["paramGroup"];
					// 		const yParamGroup = yData["paramGroup"];
					// 		let xUserPref = "NA";
					// 		let yUserPref = "NA";
					// 		paramGroup.forEach(pGroup => {
					// 			pGroup = unitService.groupTransform(
					// 				pGroup.toLowerCase()
					// 			);
					// 			let userPref = unitPref[pGroup];
					// 			userPref = unitService.unitTransform(userPref);
					// 			if (xParamGroup === pGroup) {
					// 				xUserPref = userPref;
					// 			}
					// 			if (yParamGroup === pGroup) {
					// 				yUserPref = userPref;
					// 			}
					// 		});
					// sails.log.debug(
					// 	xParamGroup,
					// 	yParamGroup,
					// 	xUserPref,
					// 	yUserPref,
					// 	resObj
					// );

					// sails.log.debug("After");
					// sails.log.debug(resObj);
					//		return res.ok({ "data": resObj });
					//	})
					//	.catch(err => {
					//		return res.ok({ "data": resObj });
					//	});
					//Unit Service Logic Ends Here
				})
				.catch(e => {
					sails.log.error(e);
					return res.serverError();
				});
		} catch (e) {
			sails.log.error(e);
			return res.badRequest("Parameters are not complete");
		}
	},
	/**
	 * @function tonnageDeliveryLoading
	 * @description used for extracting data of chillers i.e. tonnage and chiller plant
	 * efficiency which will be shown in the dashboard.
	 * @param {*} req
	 * @param {*} res
	 * @param {*} req._userMeta._site - getting site from token.
	 */
  "tonnageDeliveryLoading": async function (req, res) {
    let tonnage = undefined;
    let chleff = undefined;

    let siteId;
    if (typeof req._userMeta != "undefined") siteId = req._userMeta._site;
    if (!siteId) {
      return res.ok({ "err": "SiteId is not present." });
    }
	  {
		  /**
		   * MOCKING DASHBOARD API FOR IBMS PRESENTATION ONLY. We are using mgch site for demo purposes
		   * */
		  const sunshineIBMSSites = ['be-hyd', 'suh-hyd'];
		  if (sunshineIBMSSites.indexOf(siteId) !== -1) {
			  siteId = "mgch"
		  }
	  }
    try {
      //getting chillers from component table.
      let compArr = await Component.find({ "siteId": siteId });

      if (compArr.length <= 0) {
        return res.ok({
          "err": "No component available in this site.",
        });
      }
      let chillers = compArr.filter((component) => {
        if (component.deviceType == "chiller" || component.deviceType == "airCooledChiller" ) {
          return true;
        }
      });

      //fetching all chillers data from data device table.
      let promAr = chillers.map((chl) =>
        dataDeviceService.getDeviceData(chl.deviceId, moment(), 1, 15, [
          "chleff",
          "tptr",
        ])
      );

      let allDeviceData = await Promise.all(promAr);

      for (let chlData of allDeviceData.flat()) {
        if (chleff === undefined && chlData.data["chleff"] !== undefined) {
          chleff = helper.returnFilteredNumber(chlData.data["chleff"]);
        }
        if (tonnage === undefined && chlData.data["tptr"] !== undefined) {
          tonnage = helper.returnFilteredNumber(chlData.data["tptr"]);
        }
      }
      return res.ok({
        "data": { "tr": tonnage ? tonnage : "NA", "chleff": chleff ? chleff : "NA" },
      });
    } catch (e) {
      sails.log.error(e);
      return res.badRequest();
    }
  },

	/**
	 * @function analyseParmDiff
	 * @param {Object} req
	 * @param {string} req.body.startTime Time from which you want to start querying, preffered
	 * format is YYYY-MM-DD HH:mm:ss in IST
	 * @param {string} req.body.endTime Time till which you want to query, preffered
	 * format is YYYY-MM-DD HH:mm:ss in IST
	 * @param {string} req.body.deviceIds comma seperated list of deviceIds
	 * @param {string} req.body.params comma seperated list of parameter, currently supported parameters
	 * are kvah and runminutes
	 * @param {string} req.body.groupBy how the data should be grouped by
	 * d stands for day
	 * M stands for month
	 * h stands for hour
	 * w stands for week
	 * @param {Object} res
	 */
	"analyseParamDiff": async (req, res) => {
		let {
			startTime,
			endTime,
			deviceIds,
			params,
			groupBy,
		} = req.allParams();
		let paramArr = [];
		if (!filterParams())
			return res.badRequest({ "err": "Invalid Parameters" });
		let dataP = {};

		// Modication in usecase: Using AWS timestream ingested runningminutes values instead of component Dat
		// if (params === "runminutes"){
		// 	let resObj = await calculateRunningMinutes(startTime, endTime, deviceIds, groupBy);
		// 	return res.ok(resObj);
		// }

		for (let deviceId of deviceIds) {
			let eTime = endTime.clone();
			if (!dataP[deviceId]) dataP[deviceId] = [];
			while (eTime.isAfter(startTime)) {
				dataP[deviceId].push(
					dataDeviceService.paramDiff(
						deviceId,
						groupBy,
						eTime.clone(),
						paramArr
					)
				);
				eTime = eTime.subtract(1, groupBy);
			}
		}
		let resObj = {};
		for (let deviceId in dataP) {
			let dataPromise = dataP[deviceId];
			let data = await Promise.all(dataPromise);
			resObj[deviceId] = {};
			data.map(val => {
				let plotData = resObj[deviceId];
				for (let param of paramArr) {
					if (!plotData[param]) plotData[param] = [];
					if ((param === "runminutes") & (val[param][1] != null)) {
						//converting minutes to hours
						val[param][1] = helper.returnFilteredNumber(
							val[param][1] / 60
						);
					}
					plotData[param].push(val[param]);
				}
			});
		}
		return res.ok(resObj);
		function filterParams() {
			if (!deviceIds) return false;
			else if (typeof deviceIds === "string")
				deviceIds = deviceIds.split(",");
			else if (!Array.isArray(deviceIds)) return false;
			else return false;
			if (!groupBy) {
				groupBy = "h";
			} else if (
				groupBy != "M" &&
				groupBy != "h" &&
				groupBy != "d" &&
				groupBy != "w"
			) {
				return false;
			}
			if (!endTime) endTime = moment();
			else if (!moment(endTime, "YYYY-MM-DD").isValid()) return false;
			else endTime = moment(endTime);

			if (!startTime) startTime = moment().subtract(1, "d");
			else if (!moment(startTime, "YYYY-MM-DD").isValid()) return false;
			else startTime = moment(startTime);

			if (!params) paramArr = ["kvah", "runminutes"];
			else if (typeof params === "string") paramArr = params.split(",");
			// paramArr.push(params);
			else if (Array.isArray(params)) paramArr = params;
			return true;
		}
	},
	"saveData": async (req, res) => {
		let requestObj = {};
		let { JBData } = req.allParams();
		let dataPacket = JBData;
		if (!dataPacket) {
			return res.badRequest({ "err": "Parameters missing" });
		}
		try {
			if (typeof dataPacket === "string") {
				//Parsing incoming data to json as if data is sent by python app is string which is needed to be parsed. It may be removed if it is assured that data coming from joulebox is JSON
				let t = JSON.parse(dataPacket);
				//support for object as well as array
				requestObj = Array.isArray(t) ? t[0] : t;
			} else {
				//support for object as well as array
				requestObj = Array.isArray(dataPacket)
					? dataPacket[0]
					: dataPacket;
			}
		} catch (e) {
			sails.log.error(e);
			return res.badRequest({ "err": "Invalid Data Format" });
		}
		if (
			!requestObj.siteId ||
			!requestObj.data ||
			requestObj.data.length == 0
		) {
			return res.badRequest({
				"error":
					"API Structure Mismatch!! Please Refer To Documentation",
			});
		}

		let dataArr = requestObj.data;
		let filteredData = dataArr.map(dataPacket => {
			let { data, siteId, timestamp, deviceId } = dataPacket;
			timestamp = moment(timestamp)
				.startOf("m")
				.format("YYYY-MM-DD HH:mm:ss");
			let newDataPacket = {};
			data = helper.toJson(data);
			if (!data || Object.keys(data).length <= 0) return undefined;
			for (let key in data) {
				newDataPacket[key.toLowerCase()] = data[key];
			}
			return {
				siteId,
				timestamp,
				deviceId,
				"data": newDataPacket,
			};
		});
		let finalData = filteredData.filter(e => e != undefined);
		try {
			await Datadevice.create(finalData);
			return res.created();
		} catch (e) {
			sails.log.error(e, JSON.stringify(finalData));
			return res.serverError();
		}
	},
	"saveProtoConvertData": async (req, res) => {
		let JBData = req.param("JBData");
		// sails.log.info("ProtoData ", JSON.stringify(JBData));
		if (!JBData) {
			return res.badRequest({ "err": "Insufficient Parameters" });
		}
		let reqObj;
		if (typeof JBData == "string") {
			try {
				reqObj = JSON.parse(JBData);
			} catch (e) {
				sails.log.error(e);
				return res.badRequest({ "err": "Insufficient Parameters" });
			}
		}
		reqObj = JBData;
		if (!reqObj.timestamp || !reqObj.siteId || !reqObj.data) {
			return res.badRequest({ "err": "Insufficient Parameters" });
		}
		reqObj.data.forEach(deviceData => {
			let dataPacket = deviceData.data;
			try {
				deviceData["timestamp"] = moment(deviceData["timestamp"])
					.startOf("minute")
					.format("YYYY-MM-DD HH:mm:ss");
			} catch (e) {
				sails.log.error(e, "Error in protoconvert timestamp format");
			}

			Object.keys(dataPacket).forEach(key => {
				dataPacket[key] = helper.returnFilteredNumber(dataPacket[key]);
			});
		});
		let dataClone = reqObj.data.map(e => e);
		Datadevice.create(reqObj.data)
			.then(async d => {
				try {
					//For adding it to queue from where lambda takes over
					await sqsService.addToQueue(dataClone);
				} catch (e) {
					sails.log.error(e);
				}
				return res.created({ "msg": "Records Saved" });
			})
			.catch(err => {
				sails.log.error(err);
				return res.serverError({ "err": "Unable to save data in DB" });
			});
	},
	/**
	 * @function mainMeterConsumption
	 * @description find the main meters for a particular site and return the total consumption of site at a custom time.
	 * @param res.body.startTime - start time for the consumption.
	 * @param res.body.endTime - end time for the consumption.
	 * @param res.body.siteId - siteId for a partiular site.
	 */
	"mainMeterConsumption": async (req, res) => {
		let { startTime, endTime, siteId } = req.allParams();
		let _userPrefernce = req._userMeta.unitPref;
		let siteConsumptionUnit = await dailyConsumptionService.getUserConsumptionPreference(_userPrefernce, siteId);


		if (!siteId) return res.badRequest();
		let key = `${siteId}_mainMeter`;
		try {
			const data = await DyanmoKeyStore.findOne({ key });
			if (!data) {
				return res.badRequest();
			}
			const emList = data.value.split(",");
			const params = [siteConsumptionUnit];
			let promArr = emList.map(emId =>
				dataDeviceService.customConsumption(
					emId,
					params,
					startTime,
					endTime
				)
			);
			const allEmData = await Promise.all(promArr);
			let isValuesNull = false;
			const totalConsumptionArr = allEmData.map(device => {
				if (helper.isNullish(device.param[params[0]])) {
					isValuesNull = true;
				}
				return device.param.kvah;
			});
			if (isValuesNull) {
				return res.send({
					"err": "EM Data not present in one of the energy meters.",
				});
			}
			const totalConsumption = totalConsumptionArr.reduce(
				(sum, current) => sum + current
			);
			return res.send({ "totalConsumption": parseInt(totalConsumption) });
		} catch (e) {
			sails.log.error(e);
			return res.serverError();
		}
	},

	/**
	 * @function getCustomDailyConsumption
	 * @description function for getting custom daily consumption.
	 * @param res.body.startTime - start time for the daily consumption.
	 * @param res.body.endTime - end time for the daily consumption.
	 * @param res.body.siteId - siteId for a partiular site.
	 */
	"getCustomDailyConsumption": async (req, res) => {
		let { startTime, endTime, siteId } = req.allParams();
		if (!siteId) return res.badRequest();
		try {
			let consumption = await dailyConsumptionService.getCustomConsumption(
				siteId,
				startTime,
				endTime
			);
			return res.send({ "consumption": consumption });
		} catch (e) {
			sails.log.error(e);
			return res.serverError();
		}
	},
	"getSiteConsumption": async (req, res) => {
		try {
			let siteId = req._userMeta._site;
			let _userPrefernce = req._userMeta.unitPref;
			let userconsumptionUnit = await dailyConsumptionService.getUserConsumptionPreference(_userPrefernce, siteId);
			let segregrateTS, didDeviceDataObject = {}, $devicesData,
				deviceWiseConsumption, startTimeString;

			let { startTime, endTime, groupBy, emList } = req.body;
			if (!startTime || !endTime || !emList) {
				return res.badRequest("Error! Missing params");
			}
			try {
				startTime = moment(startTime);
				endTime = moment(endTime);
			} catch (e) {
				return res.badRequest("Not a valid request start/end time");
			}
			if (!groupBy) {
				groupBy = "day"; // default groupBy
			}
			if (!dataDeviceService.isValidGroupBy(groupBy)) {
				return res.badRequest("Not a valid group by");
			}

			startTime = startTime.startOf(groupBy);
			endTime = endTime.startOf(groupBy).add(1, groupBy);
			startTimeString = startTime.format("YYYY-MM-DD HH:mm");

			if (startTime > endTime) {
				return res.forbidden("Start time cant exceed End Time");
			}

			segregrateTS = dataDeviceService.segregateTimeByGroupBy(startTime, endTime, groupBy);
			if (segregrateTS.length === 0) {
				return res.forbidden("Cannot query so much data");
			}
			for (let index in emList) {
				let deviceId = emList[index];
				$devicesData = segregrateTS.map(ts =>
					dataDeviceService.getDeviceData(deviceId, ts, 15, 15)
				);
				didDeviceDataObject[deviceId] = await Promise.all($devicesData);
			}
			deviceWiseConsumption = dataDeviceService.calculateConsumption(didDeviceDataObject, userconsumptionUnit, groupBy, startTimeString); // this kwh can be changed accordingly to prefernce
			// Reformatting response based on FE requirement.
			const returnObject = {
				data: deviceWiseConsumption,
				columns: segregrateTS,
			};
			return res.ok(returnObject);

		} catch (e) {
			sails.log.error(e);
			return res.serverError("Error");
		}
	},
	/**
	* Function to calucalte different presets graphs for rama
	* @param {Object} nodes req Request object
	*/
	"presetsRama": async (req, res) => {
		try {
			let siteId = req._userMeta._site;
			let { preset, startDate, endDate, groupBy } = req.body;
			let startDateMoment, endDateMoment, data, deviceList, dates, plantId;

			if (RAMA_PRESET.indexOf(preset) === -1) {
				sails.log(groupBy); // just to avoid esLint, will delete this line
				return res.badRequest({ "problems": ["Invalid preset"] });
			}
			if (!startDate) {
				return res.badRequest({ "problems": ["Invalid Start/End Date"] });
			}
			startDateMoment = moment(startDate);
			endDateMoment = moment(endDate);
			if (startDateMoment > endDateMoment) {
				return res.badRequest({ "problems": ["Start should be less than End Date"] });
			}
			switch (preset) {
				case "runhour":
					deviceList = req.body.deviceList;
					deviceList = helper.toArray(deviceList);
					if (!deviceList || deviceList.length === 0) {
						return res.badRequest("Missing deviceList");
					}
					dates = dataDeviceService.segregateTimeByGroupBy(startDate, endDate, "day");
					data = await dataDeviceService.runHourAnalysisRama(siteId, dates, deviceList);
					break;
				case "tonsteamperproduct":
					plantId = req.body.plantId;
					if (!plantId || typeof plantId !== "string") {
						return res.badRequest("Missing Plant Id");
					}
					data = await dataDeviceService.tonsteamperproductRama(siteId, startDate, endDate, plantId);
					break;
				case "tonelecperproduct":
					plantId = req.body.plantId;
					if (!plantId || typeof plantId !== "string") {
						return res.badRequest("Missing Plant Id");
					}
					data = await dataDeviceService.tonElectricityperproductRama(siteId, startDate, endDate, plantId);
					break;
				default:
					break;
			}
			return res.send(data);


		} catch (e) {
			sails.log.error(e);
			return res.serverError();
		}
	},
};
