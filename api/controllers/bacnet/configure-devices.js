const bacnetService = require('../../services/bacnet/bacnet.service');

/**
 * Configure BACnet devices - handles both newly discovered devices and existing device location updates
 * This API handles the "Save & Next" functionality from the BACnet device configuration UI
 */
module.exports = {
  friendlyName: 'Configure BACnet Devices',
  description: 'Configure newly discovered BACnet devices as third-party master controllers and update existing device locations',

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
      description: 'User meta information added by default to authenticated routes'
    },

    siteId: {
      type: 'string',
      required: true,
      description: 'Site ID where devices should be configured'
    },

    bacnetSlaveControllerId: {
      type: 'string',
      required: true,
      description: 'BACnet slave controller ID (deviceId in DynamoDB)'
    },

    devices: {
      type: 'ref',
      required: true,
      description: 'Array of BACnet devices to configure with their location assignments',
      example: [
        {
          bacnetDeviceId: "1",
          controllerId: null, // null for new devices, deviceId for existing
          name: "AHU-01", // device name from bacnetProperties
          location: {
            areaId: "roof-area",
            regionId: "hvac-region",
            leafNodeId: null // for IBMS sites
          }
        }
      ]
    }
  },

  exits: {
    success: {
      description: 'Devices saved/updated successfully',
      outputType: 'ref'
    },

    badRequest: {
      description: 'Invalid input parameters',
      responseType: 'badRequest'
    },

    notFound: {
      description: 'Site or controller not found',
      responseType: 'notFound'
    },

    conflict: {
      description: 'Device already exists or other conflict',
      responseType: 'conflict'
    },

    serverError: {
      description: 'Internal server error',
      responseType: 'serverError'
    }
  },

  fn: async function (inputs, exits) {
    const { siteId, bacnetSlaveControllerId, devices } = inputs;

    try {
      // Validate inputs
      if (!devices || !Array.isArray(devices) || devices.length === 0) {
        throw new Error('Devices array is required and must not be empty');
      }

      sails.log.info(`Configuring ${devices.length} BACnet devices for site: ${siteId}, slave controller: ${bacnetSlaveControllerId}`);

      // Use service to configure devices
      const results = await bacnetService.configureBacnetDevices(siteId, bacnetSlaveControllerId, devices);

      const response = {
        message: `Successfully configured ${devices.length} BACnet devices`,
        summary: {
          total: devices.length,
          newDevicesCreated: results.newDevicesCreated.length,
          existingDevicesUpdated: results.existingDevicesUpdated.length,
          errors: results.errors.length
        },
        results,
        errors: results.errors.length > 0 ? results.errors : undefined
      };

      sails.log.info(`BACnet device configuration completed: ${results.newDevicesCreated.length} created, ${results.existingDevicesUpdated.length} updated, ${results.errors.length} errors`);

      return exits.success(response);

    } catch (error) {
      sails.log.error('[BACnet > configure-devices] Error:', error);

      // Return appropriate exit based on error type
      if (error.name === 'NotFoundError') {
        return exits.notFound({ message: error.message });
      }

      return exits.serverError({
        message: 'Failed to configure BACnet devices',
        error: error.message
      });
    }
  }
};
