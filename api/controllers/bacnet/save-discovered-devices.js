const moment = require('moment-timezone');
const BACnetErrorHandler = require('../../utils/bacnet/error-handler.util');

/**
 * Save discovered BACnet devices as third-party master controllers
 * This API handles the "Save" functionality from the discovery UI
 */
module.exports = {
  friendlyName: 'Save Discovered BACnet Devices',
  description: 'Save discovered BACnet devices as third-party master controllers in the system',

  inputs: {
    siteId: {
      type: 'string',
      required: true,
      description: 'Site ID where devices should be saved'
    },

    controllerId: {
      type: 'string',
      required: true,
      description: 'BACnet slave controller ID that performed the discovery'
    },

    jobId: {
      type: 'string',
      required: true,
      description: 'Discovery job ID'
    },

    selectedDevices: {
      type: 'ref',
      required: true,
      description: 'Array of selected BACnet devices to save as controllers (from existing fetch API format)',
      example: [
        {
          bacnetDeviceId: "1",
          location: {
            areaId: "roof-area",
            regionId: "hvac-region",
            leafNodeId: null
          },
          bacnetProperties: {
            name: "AHU-01",
            address: "*************:47808",
            vendorName: "Johnson Controls",
            modelName: "AHU-Model-X",
            systemStatus: "operational",
            description: "Air Handling Unit 01",
            location: "Roof Level 1"
          }
        }
      ]
    }
  },

  exits: {
    success: {
      description: 'Devices saved successfully',
      outputType: 'ref'
    },

    badRequest: {
      description: 'Invalid input parameters',
      responseType: 'badRequest'
    },

    notFound: {
      description: 'Site, controller, or job not found',
      responseType: 'notFound'
    },

    conflict: {
      description: 'Device already exists or other conflict',
      responseType: 'conflict'
    },

    serverError: {
      description: 'Internal server error',
      responseType: 'serverError'
    }
  },

  fn: async function (inputs, exits) {
    const { siteId, controllerId, jobId, selectedDevices } = inputs;

    try {
      // Validate inputs
      BACnetErrorHandler.validateDiscoveryRequest(siteId, controllerId);

      if (!jobId || typeof jobId !== 'string') {
        throw new Error('Job ID is required and must be a string');
      }

      if (!selectedDevices || !Array.isArray(selectedDevices) || selectedDevices.length === 0) {
        throw new Error('Selected devices array is required and must not be empty');
      }

      sails.log.info(`Saving ${selectedDevices.length} discovered BACnet devices for site: ${siteId}, job: ${jobId}`);

      // Validate site exists
      const site = await sails.models.sites.findOne({ id: siteId });
      if (!site) {
        throw Object.assign(new Error('Site not found'), { name: 'NotFoundError' });
      }

      // Validate BACnet slave controller exists
      const slaveController = await sails.models.devices.findOne({
        deviceId: controllerId,
        siteId: siteId,
        isSlaveController: 1
      });

      if (!slaveController) {
        throw Object.assign(new Error('BACnet slave controller not found'), { name: 'NotFoundError' });
      }

      // Note: No need to find primary controller since BACnet masters are independent

      // Validate job exists and is completed
      const jobKey = `BACnetDiscovery:job:${jobId}`;
      const jobData = await CacheService.hgetall(jobKey);

      if (!jobData || !jobData.jobId) {
        throw Object.assign(new Error('Discovery job not found'), { name: 'NotFoundError' });
      }

      if (jobData.status !== 'completed') {
        throw Object.assign(new Error('Discovery job is not completed yet'), { name: 'ConflictError' });
      }

      // Get next device IDs for the new controllers
      const deviceIdCount = await sails.models.dynamokeystore.findOne({ key: 'totalDeviceCount' });
      let nextDeviceId = deviceIdCount ? parseInt(deviceIdCount.value) + 1 : 1;

      const savedDevices = [];
      const errors = [];

      // Process each selected device
      for (const deviceData of selectedDevices) {
        try {
          // Extract data from existing API format
          const { bacnetDeviceId, location, bacnetProperties } = deviceData;

          // Validate required fields
          if (!bacnetDeviceId || !bacnetProperties || !bacnetProperties.address || !bacnetProperties.name) {
            throw new Error(`Device missing required fields: bacnetDeviceId, address and name are required`);
          }

          // Check if device with this name already exists
          const existingDevice = await sails.models.devices.findOne({
            siteId: siteId,
            name: bacnetProperties.name
          });

          if (existingDevice) {
            errors.push({
              device: deviceData,
              error: `Device with name '${bacnetProperties.name}' already exists`
            });
            continue;
          }

          // Use location from payload or defaults
          const networkId = location?.networkId || Object.keys(site.networks)[0];
          const regionId = location?.regionId || Object.keys(site.regions)[0];
          const areaId = location?.areaId || Object.keys(site.areas || {})[0];

          // Validate network and region exist in site
          if (networkId && !site.networks[networkId]) {
            throw new Error(`Network '${networkId}' does not exist in site`);
          }

          if (regionId && !site.regions[regionId]) {
            throw new Error(`Region '${regionId}' does not exist in site`);
          }

          // Create device configuration for discovered BACnet third-party controller
          const deviceConfig = {
            deviceId: String(nextDeviceId),
            siteId: siteId,
            name: bacnetProperties.name,
            deviceType: 'n3uronbacnetmqtt', // From vendors.json - UI recognition only
            networkId: networkId,
            regionId: regionId,
            areaId: areaId,

            // BACnet device information
            IPAddress: bacnetProperties.address.split(':')[0], // Extract IP from address
            vendorId: 'n3uronbacnetmqtt',
            hardwareVer: bacnetProperties.modelName || 'Unknown',
            softwareVer: 'v1',
            operationMode: 'network',

            // Third-party master controller configuration
            isSlaveController: false, // This is a MASTER controller (0 = master, 1 = slave)
            // No controllerId - master controllers are independent
            secondaryControllerId: controllerId, // Communication path via N3uron slave controller
            communicationType: 'IoT', // No direct communication - managed via parent controller
            communicationCategory: 'IoT',
            driverType: '0', // No driver needed - third-party controller
            functionType: 'IoT',

            // Additional metadata
            deviceMeta: {
              bacnetDeviceId: bacnetDeviceId, // Link to PostgreSQL BMSDevice
              bacnetAddress: bacnetProperties.address,
              systemStatus: bacnetProperties.systemStatus,
              description: bacnetProperties.description,
              vendorName: bacnetProperties.vendorName,
              modelName: bacnetProperties.modelName,
              location: bacnetProperties.location,
              discoveredAt: moment.tz('UTC').toISOString(),
              discoveryJobId: jobId,
              discoveredBy: controllerId, // N3uron slave controller that discovered it
              discoveryMethod: 'bacnet_n3uron' // For future device creation reference
            },

            status: 1,
            isConfigured: '1'
          };

          // Create the device
          const newDevice = await sails.models.devices.create(deviceConfig);

          // Update BMSDevice.refDeviceId to link to the new controller
          await sails.models.bmsdevice.updateOne({
            id: parseInt(bacnetDeviceId)
          }).set({
            refDeviceId: parseInt(newDevice.deviceId)
          });

          // Update site networks and regions to include this controller
          const updatedSite = { ...site };

          if (updatedSite.networks[deviceConfig.networkId]) {
            if (!updatedSite.networks[deviceConfig.networkId].includes(String(nextDeviceId))) {
              updatedSite.networks[deviceConfig.networkId].push(String(nextDeviceId));
            }
          }

          if (updatedSite.regions[deviceConfig.regionId] && updatedSite.regions[deviceConfig.regionId].controller) {
            if (!updatedSite.regions[deviceConfig.regionId].controller.includes(String(nextDeviceId))) {
              updatedSite.regions[deviceConfig.regionId].controller.push(String(nextDeviceId));
            }
          }

          // Update site with new controller references
          await sails.models.sites.updateOne({ id: siteId }).set({
            networks: updatedSite.networks,
            regions: updatedSite.regions
          });

          // Update total device count
          await sails.models.dynamokeystore.updateOne({ key: 'totalDeviceCount' }).set({ value: String(nextDeviceId) });

          // Update site config timestamp
          await sails.models.dynamokeystore.updateOne({ key: `configTs:${siteId}` }).set({
            value: moment.tz('UTC').toISOString()
          });

          savedDevices.push({
            deviceId: newDevice.deviceId,
            name: newDevice.name,
            address: bacnetProperties.address,
            bacnetDeviceId: bacnetDeviceId,
            status: 'created'
          });

          nextDeviceId++;

          sails.log.info(`Created BACnet controller device: ${newDevice.deviceId} - ${newDevice.name}, linked to BMSDevice: ${bacnetDeviceId}`);

        } catch (deviceError) {
          sails.log.error(`Error creating device ${bacnetProperties.name}:`, deviceError);
          errors.push({
            device: deviceData,
            error: deviceError.message
          });
        }
      }

      // Send WebSocket notification about new devices
      if (savedDevices.length > 0) {
        await SocketService.notifyJouleTrackPublicRoom(siteId, 'devices', {
          event: 'create',
          data: savedDevices.map(d => ({ deviceId: d.deviceId, name: d.name }))
        });
      }

      // Update job status to indicate devices were saved
      await CacheService.hmset(jobKey, {
        devicesSaved: savedDevices.length,
        savedAt: moment.tz('UTC').toISOString(),
        savedDevices: JSON.stringify(savedDevices.map(d => d.deviceId))
      });

      const response = {
        message: `Successfully saved ${savedDevices.length} BACnet devices as controllers`,
        savedDevices,
        errors: errors.length > 0 ? errors : undefined,
        summary: {
          total: selectedDevices.length,
          saved: savedDevices.length,
          failed: errors.length
        }
      };

      sails.log.info(`BACnet device save operation completed for job ${jobId}: ${savedDevices.length} saved, ${errors.length} failed`);

      return exits.success(response);

    } catch (error) {
      const errorResponse = BACnetErrorHandler.handleDiscoveryRequestError(error, siteId, controllerId, jobId);

      // Return appropriate exit based on status code
      switch (errorResponse.statusCode) {
        case 400:
          return exits.badRequest(errorResponse);
        case 404:
          return exits.notFound(errorResponse);
        case 409:
          return exits.conflict(errorResponse);
        default:
          return exits.serverError(errorResponse);
      }
    }
  }
};
