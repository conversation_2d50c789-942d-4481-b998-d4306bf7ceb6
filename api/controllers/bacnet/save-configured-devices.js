const moment = require('moment-timezone');

/**
 * Configure BACnet devices - handles both newly discovered devices and existing device location updates
 * This API handles the "Save & Next" functionality from the BACnet device configuration UI
 */
module.exports = {
  friendlyName: 'Configure BACnet Devices',
  description: 'Save newly discovered BACnet devices as third-party master controllers and update existing device locations',

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
      description: 'User meta information added by default to authenticated routes'
    },

    siteId: {
      type: 'string',
      required: true,
      description: 'Site ID where devices should be configured'
    },

    bacnetSlaveControllerId: {
      type: 'string',
      required: true,
      description: 'BACnet slave controller ID (deviceId in DynamoDB)'
    },

    devices: {
      type: 'ref',
      required: true,
      description: 'Array of BACnet devices to configure with their location assignments',
      example: [
        {
          bacnetDeviceId: "1",
          controllerId: null, // null for new devices, deviceId for existing
          name: "AHU-01", // device name from bacnetProperties
          location: {
            areaId: "roof-area",
            regionId: "hvac-region",
            leafNodeId: null // for IBMS sites
          }
        }
      ]
    }
  },

  exits: {
    success: {
      description: 'Devices saved/updated successfully',
      outputType: 'ref'
    },

    badRequest: {
      description: 'Invalid input parameters',
      responseType: 'badRequest'
    },

    notFound: {
      description: 'Site or controller not found',
      responseType: 'notFound'
    },

    conflict: {
      description: 'Device already exists or other conflict',
      responseType: 'conflict'
    },

    serverError: {
      description: 'Internal server error',
      responseType: 'serverError'
    }
  },

  fn: async function (inputs, exits) {
    const { siteId, bacnetSlaveControllerId, devices } = inputs;

    try {
      // Validate inputs
      if (!devices || !Array.isArray(devices) || devices.length === 0) {
        throw new Error('Devices array is required and must not be empty');
      }

      sails.log.info(`Configuring ${devices.length} BACnet devices for site: ${siteId}, slave controller: ${bacnetSlaveControllerId}`);

      // Validate site exists
      const site = await sails.models.sites.findOne({ id: siteId });
      if (!site) {
        throw Object.assign(new Error('Site not found'), { name: 'NotFoundError' });
      }

      // Check if site is IBMS to determine location structure
      const isIBMS = site.industryType && site.industryType.includes("ibms");

      // Validate BACnet slave controller exists
      const slaveController = await sails.models.devices.findOne({
        deviceId: bacnetSlaveControllerId,
        siteId: siteId,
        isSlaveController: 1
      });

      if (!slaveController) {
        throw Object.assign(new Error('BACnet slave controller not found'), { name: 'NotFoundError' });
      }

      // Separate new devices from existing devices
      const newDevices = devices.filter(device => !device.controllerId);
      const existingDevices = devices.filter(device => device.controllerId);

      sails.log.info(`Found ${newDevices.length} new devices and ${existingDevices.length} existing devices to configure`);

      const results = {
        newDevicesCreated: [],
        existingDevicesUpdated: [],
        errors: []
      };

      // Process new devices
      if (newDevices.length > 0) {
        const newDeviceResults = await this.processNewDevices(newDevices, site, slaveController, isIBMS);
        results.newDevicesCreated = newDeviceResults.created;
        results.errors.push(...newDeviceResults.errors);
      }

      // Process existing devices
      if (existingDevices.length > 0) {
        const existingDeviceResults = await this.processExistingDevices(existingDevices, site, isIBMS);
        results.existingDevicesUpdated = existingDeviceResults.updated;
        results.errors.push(...existingDeviceResults.errors);
      }

      // Send WebSocket notifications
      if (results.newDevicesCreated.length > 0 || results.existingDevicesUpdated.length > 0) {
        await SocketService.notifyJouleTrackPublicRoom(siteId, 'devices', {
          event: 'bacnet_devices_configured',
          data: {
            newDevices: results.newDevicesCreated.map(d => ({ deviceId: d.deviceId, name: d.name })),
            updatedDevices: results.existingDevicesUpdated.map(d => ({ deviceId: d.deviceId, name: d.name }))
          }
        });
      }

      const response = {
        message: `Successfully configured ${devices.length} BACnet devices`,
        summary: {
          total: devices.length,
          newDevicesCreated: results.newDevicesCreated.length,
          existingDevicesUpdated: results.existingDevicesUpdated.length,
          errors: results.errors.length
        },
        results,
        errors: results.errors.length > 0 ? results.errors : undefined
      };

      sails.log.info(`BACnet device configuration completed: ${results.newDevicesCreated.length} created, ${results.existingDevicesUpdated.length} updated, ${results.errors.length} errors`);

      return exits.success(response);

    } catch (error) {
      sails.log.error('[BACnet > save-configured-devices] Error:', error);

      // Return appropriate exit based on error type
      if (error.name === 'NotFoundError') {
        return exits.notFound({ message: error.message });
      }

      return exits.serverError({
        message: 'Failed to configure BACnet devices',
        error: error.message
      });
    }
  },

  // Helper method to process new devices
  processNewDevices: async function (newDevices, site, slaveController, isIBMS) {
    const created = [];
    const errors = [];

    // Get next device IDs for the new controllers
    const deviceIdCount = await sails.models.dynamokeystore.findOne({ key: 'totalDeviceCount' });
    let nextDeviceId = deviceIdCount ? parseInt(deviceIdCount.value) + 1 : 1;

    for (const deviceData of newDevices) {
      try {
        const { bacnetDeviceId, location, name } = deviceData;

        // Validate required fields
        if (!bacnetDeviceId || !name) {
          throw new Error(`Device missing required fields: bacnetDeviceId and name are required`);
        }

        // Check if device with this name already exists
        const existingDevice = await sails.models.devices.findOne({
          siteId: site.id,
          name: name
        });

        if (existingDevice) {
          errors.push({
            device: deviceData,
            error: `Device with name '${name}' already exists`
          });
          continue;
        }

        // Determine location based on site type and provided data
        let locationConfig = {};
        if (isIBMS) {
          locationConfig.leafNodeId = location?.leafNodeId;
        } else {
          locationConfig.networkId = location?.networkId || Object.keys(site.networks)[0];
          locationConfig.regionId = location?.regionId || Object.keys(site.regions)[0];
          locationConfig.areaId = location?.areaId || Object.keys(site.areas || {})[0];
        }

        // Validate location exists in site
        if (!isIBMS) {
          if (locationConfig.networkId && !site.networks[locationConfig.networkId]) {
            throw new Error(`Network '${locationConfig.networkId}' does not exist in site`);
          }
          if (locationConfig.regionId && !site.regions[locationConfig.regionId]) {
            throw new Error(`Region '${locationConfig.regionId}' does not exist in site`);
          }
        }

        // Create device configuration for discovered BACnet third-party master controller
        const deviceConfig = {
          deviceId: String(nextDeviceId),
          siteId: site.id,
          name: name,
          deviceType: 'n3uronbacnetmqtt-controller', // From vendors.json
          vendorId: 'n3uronbacnetmqtt',
          ...locationConfig,

          // Third-party master controller configuration
          isSlaveController: 0, // This is a MASTER controller (0 = master, 1 = slave)
          // No controllerId - master controllers don't have controllerId
          secondaryControllerId: slaveController.deviceId, // Communication path via N3uron slave controller
          communicationType: 'IoT',
          communicationCategory: 'IoT',

          status: 1,
          isConfigured: '1'
        };

        // Create the device
        const newDevice = await sails.models.devices.create(deviceConfig);

        // Update BMSDevice.refDeviceId to link to the new controller
        await sails.models.bmsdevice.updateOne({
          id: parseInt(bacnetDeviceId)
        }).set({
          refDeviceId: parseInt(newDevice.deviceId)
        });

        // Update site networks and regions to include this controller (for non-IBMS sites)
        if (!isIBMS) {
          const updatedSite = { ...site };

          if (updatedSite.networks[deviceConfig.networkId]) {
            if (!updatedSite.networks[deviceConfig.networkId].includes(String(nextDeviceId))) {
              updatedSite.networks[deviceConfig.networkId].push(String(nextDeviceId));
            }
          }

          if (updatedSite.regions[deviceConfig.regionId] && updatedSite.regions[deviceConfig.regionId].controller) {
            if (!updatedSite.regions[deviceConfig.regionId].controller.includes(String(nextDeviceId))) {
              updatedSite.regions[deviceConfig.regionId].controller.push(String(nextDeviceId));
            }
          }

          // Update site with new controller references
          await sails.models.sites.updateOne({ id: site.id }).set({
            networks: updatedSite.networks,
            regions: updatedSite.regions
          });
        }

        // Update total device count
        await sails.models.dynamokeystore.updateOne({ key: 'totalDeviceCount' }).set({ value: String(nextDeviceId) });

        // Update site config timestamp
        await sails.models.dynamokeystore.updateOne({ key: `configTs:${site.id}` }).set({
          value: moment.tz('UTC').toISOString()
        });

        created.push({
          deviceId: newDevice.deviceId,
          name: newDevice.name,
          bacnetDeviceId: bacnetDeviceId,
          status: 'created'
        });

        nextDeviceId++;

        sails.log.info(`Created BACnet controller device: ${newDevice.deviceId} - ${newDevice.name}, linked to BMSDevice: ${bacnetDeviceId}`);

      } catch (deviceError) {
        sails.log.error(`Error creating device ${deviceData.name}:`, deviceError);
        errors.push({
          device: deviceData,
          error: deviceError.message
        });
      }
    }

    return { created, errors };
  },

  // Helper method to process existing devices (location updates)
  processExistingDevices: async function (existingDevices, site, isIBMS) {
    const updated = [];
    const errors = [];

    for (const deviceData of existingDevices) {
      try {
        const { controllerId, location, name } = deviceData;

        if (!controllerId) {
          throw new Error('Controller ID is required for existing devices');
        }

        // Find the existing device
        const existingDevice = await sails.models.devices.findOne({
          deviceId: controllerId,
          siteId: site.id
        });

        if (!existingDevice) {
          errors.push({
            device: deviceData,
            error: `Device with ID '${controllerId}' not found`
          });
          continue;
        }

        // Prepare update data based on site type
        let updateData = {};
        let locationChanged = false;

        if (isIBMS) {
          // For IBMS sites, handle leafNodeId updates
          if (location?.leafNodeId && location.leafNodeId !== existingDevice.leafNodeId) {
            updateData.leafNodeId = location.leafNodeId;
            locationChanged = true;
          }
        } else {
          // For non-IBMS sites, handle areaId and regionId updates
          if (location?.areaId && location.areaId !== existingDevice.areaId) {
            if (!site.areas || !site.areas[location.areaId]) {
              throw new Error(`Area '${location.areaId}' does not exist in site`);
            }
            updateData.areaId = location.areaId;
            locationChanged = true;
          }

          if (location?.regionId && location.regionId !== existingDevice.regionId) {
            if (!site.regions[location.regionId]) {
              throw new Error(`Region '${location.regionId}' does not exist in site`);
            }
            updateData.regionId = location.regionId;
            locationChanged = true;
          }

          if (location?.networkId && location.networkId !== existingDevice.networkId) {
            if (!site.networks[location.networkId]) {
              throw new Error(`Network '${location.networkId}' does not exist in site`);
            }
            updateData.networkId = location.networkId;
            locationChanged = true;
          }
        }

        // Update device name if provided and different
        if (name && name !== existingDevice.name) {
          // Check if new name conflicts with another device
          const nameConflict = await sails.models.devices.findOne({
            siteId: site.id,
            name: name,
            deviceId: { '!=': controllerId }
          });

          if (nameConflict) {
            throw new Error(`Device name '${name}' is already in use by another device`);
          }

          updateData.name = name;
        }

        // Only update if there are changes
        if (Object.keys(updateData).length === 0) {
          sails.log.info(`No changes detected for device ${controllerId}, skipping update`);
          continue;
        }

        // Update the device
        const updatedDevice = await sails.models.devices.updateOne({
          deviceId: controllerId,
          siteId: site.id
        }).set(updateData);

        // Update site networks and regions if location changed (for non-IBMS sites)
        if (locationChanged && !isIBMS) {
          await this.updateSiteLocationReferences(site, existingDevice, updatedDevice);
        }

        // Update site config timestamp
        await sails.models.dynamokeystore.updateOne({ key: `configTs:${site.id}` }).set({
          value: moment.tz('UTC').toISOString()
        });

        updated.push({
          deviceId: updatedDevice.deviceId,
          name: updatedDevice.name,
          changes: updateData,
          status: 'updated'
        });

        sails.log.info(`Updated BACnet controller device: ${updatedDevice.deviceId} - ${updatedDevice.name}`);

      } catch (deviceError) {
        sails.log.error(`Error updating device ${deviceData.controllerId}:`, deviceError);
        errors.push({
          device: deviceData,
          error: deviceError.message
        });
      }
    }

    return { updated, errors };
  },

  // Helper method to update site location references when device location changes
  updateSiteLocationReferences: async function (site, oldDevice, newDevice) {
    try {
      const updatedSite = { ...site };
      const deviceId = newDevice.deviceId;

      // Remove device from old network if network changed
      if (oldDevice.networkId !== newDevice.networkId) {
        if (oldDevice.networkId && updatedSite.networks[oldDevice.networkId]) {
          updatedSite.networks[oldDevice.networkId] = updatedSite.networks[oldDevice.networkId].filter(id => id !== deviceId);
        }

        // Add device to new network
        if (newDevice.networkId && updatedSite.networks[newDevice.networkId]) {
          if (!updatedSite.networks[newDevice.networkId].includes(deviceId)) {
            updatedSite.networks[newDevice.networkId].push(deviceId);
          }
        }
      }

      // Remove device from old region if region changed
      if (oldDevice.regionId !== newDevice.regionId) {
        if (oldDevice.regionId && updatedSite.regions[oldDevice.regionId] && updatedSite.regions[oldDevice.regionId].controller) {
          updatedSite.regions[oldDevice.regionId].controller = updatedSite.regions[oldDevice.regionId].controller.filter(id => id !== deviceId);
        }

        // Add device to new region
        if (newDevice.regionId && updatedSite.regions[newDevice.regionId] && updatedSite.regions[newDevice.regionId].controller) {
          if (!updatedSite.regions[newDevice.regionId].controller.includes(deviceId)) {
            updatedSite.regions[newDevice.regionId].controller.push(deviceId);
          }
        }
      }

      // Update site with new references
      await sails.models.sites.updateOne({ id: site.id }).set({
        networks: updatedSite.networks,
        regions: updatedSite.regions
      });

      sails.log.info(`Updated site location references for device ${deviceId}`);

    } catch (error) {
      sails.log.error(`Error updating site location references:`, error);
      throw error;
    }
  }
};
